/**
 * IndexedDB Storage Manager for Clothing Store
 * Supports 50MB-1GB+ storage for unlimited products
 */

class IndexedDBManager {
    constructor() {
        this.dbName = 'VaithClothingStore';
        this.version = 1;
        this.db = null;
    }

    async init() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.version);

            request.onerror = () => reject(request.error);
            request.onsuccess = () => {
                this.db = request.result;
                resolve(this.db);
            };

            request.onupgradeneeded = (event) => {
                const db = event.target.result;

                // Create products store
                if (!db.objectStoreNames.contains('products')) {
                    const productStore = db.createObjectStore('products', { keyPath: 'id' });
                    productStore.createIndex('category', 'category', { unique: false });
                    productStore.createIndex('name', 'name', { unique: false });
                }

                // Create orders store
                if (!db.objectStoreNames.contains('orders')) {
                    const orderStore = db.createObjectStore('orders', { keyPath: 'id' });
                    orderStore.createIndex('status', 'status', { unique: false });
                    orderStore.createIndex('customerEmail', 'customerEmail', { unique: false });
                }

                // Create pending orders store
                if (!db.objectStoreNames.contains('pendingOrders')) {
                    db.createObjectStore('pendingOrders', { keyPath: 'id' });
                }
            };
        });
    }

    // Product methods
    async addProduct(product) {
        const transaction = this.db.transaction(['products'], 'readwrite');
        const store = transaction.objectStore('products');
        return store.add(product);
    }

    async getAllProducts() {
        const transaction = this.db.transaction(['products'], 'readonly');
        const store = transaction.objectStore('products');
        return new Promise((resolve, reject) => {
            const request = store.getAll();
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async updateProduct(product) {
        const transaction = this.db.transaction(['products'], 'readwrite');
        const store = transaction.objectStore('products');
        return store.put(product);
    }

    async deleteProduct(id) {
        const transaction = this.db.transaction(['products'], 'readwrite');
        const store = transaction.objectStore('products');
        return store.delete(id);
    }

    // Order methods
    async addOrder(order) {
        const transaction = this.db.transaction(['orders'], 'readwrite');
        const store = transaction.objectStore('orders');
        return store.add(order);
    }

    async getAllOrders() {
        const transaction = this.db.transaction(['orders'], 'readonly');
        const store = transaction.objectStore('orders');
        return new Promise((resolve, reject) => {
            const request = store.getAll();
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    // Migration from localStorage
    async migrateFromLocalStorage() {
        try {
            // Migrate products
            const products = JSON.parse(localStorage.getItem('vaith_products') || '[]');
            for (const product of products) {
                await this.addProduct(product);
            }

            // Migrate orders
            const orders = JSON.parse(localStorage.getItem('vaith_orders') || '[]');
            for (const order of orders) {
                await this.addOrder(order);
            }

            console.log(`Migrated ${products.length} products and ${orders.length} orders to IndexedDB`);
            return { products: products.length, orders: orders.length };
        } catch (error) {
            console.error('Migration error:', error);
            throw error;
        }
    }

    // Storage info
    async getStorageInfo() {
        if ('storage' in navigator && 'estimate' in navigator.storage) {
            const estimate = await navigator.storage.estimate();
            return {
                quota: estimate.quota,
                usage: estimate.usage,
                available: estimate.quota - estimate.usage,
                quotaMB: (estimate.quota / (1024 * 1024)).toFixed(2),
                usageMB: (estimate.usage / (1024 * 1024)).toFixed(2),
                availableMB: ((estimate.quota - estimate.usage) / (1024 * 1024)).toFixed(2)
            };
        }
        return null;
    }
}

// Global instance
window.indexedDBManager = new IndexedDBManager();

// Helper functions for easy migration
window.upgradeToIndexedDB = async function() {
    try {
        if (!confirm('Upgrade to IndexedDB for unlimited products? This will migrate your existing data.')) {
            return;
        }

        await indexedDBManager.init();
        const migrationResult = await indexedDBManager.migrateFromLocalStorage();
        
        alert(`✅ Successfully upgraded to IndexedDB!\n\nMigrated:\n• ${migrationResult.products} products\n• ${migrationResult.orders} orders\n\nYou can now add 1000+ products!`);
        
        // Optional: Clear localStorage after successful migration
        if (confirm('Clear old localStorage data? (Recommended after successful migration)')) {
            localStorage.removeItem('vaith_products');
            localStorage.removeItem('vaith_orders');
            localStorage.removeItem('vaith_pending_orders');
        }
        
    } catch (error) {
        alert(`❌ Upgrade failed: ${error.message}`);
        console.error('IndexedDB upgrade error:', error);
    }
};

window.checkIndexedDBStorage = async function() {
    try {
        await indexedDBManager.init();
        const info = await indexedDBManager.getStorageInfo();
        
        if (info) {
            alert(`📊 INDEXEDDB STORAGE INFO:\n\n` +
                `💾 Total Quota: ${info.quotaMB} MB\n` +
                `📈 Used: ${info.usageMB} MB\n` +
                `🆓 Available: ${info.availableMB} MB\n\n` +
                `🎯 Capacity: ~${Math.floor(info.available / (50 * 1024))} more products\n` +
                `(Assuming 50KB per product with images)`);
        } else {
            alert('Storage estimation not available in this browser');
        }
    } catch (error) {
        alert(`Error checking storage: ${error.message}`);
    }
};
